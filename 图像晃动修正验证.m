% 图像晃动修正验证程序
% 用于快速验证修正效果，生成修正前后的对比动画
clear all; close all; clc;

%% 参数设置
imageFolder = '.'; % 当前目录
imageExt = '*.JPG';
savePath = fullfile(imageFolder, 'verification_results');
if ~exist(savePath, 'dir'), mkdir(savePath); end

% 读取图像
imageFiles = dir(fullfile(imageFolder, imageExt));
numFrames = length(imageFiles);
if numFrames < 2
    error('图像数量不足');
end
[~, idx] = sort([imageFiles.datenum]);
imageFiles = imageFiles(idx);

fprintf('读取到 %d 张图像\n', numFrames);

% 读取第一帧作为参考
frame1 = imread(fullfile(imageFolder, imageFiles(1).name));
if size(frame1,3) == 1
    frame1 = cat(3, frame1, frame1, frame1);
end
[frameH, frameW, ~] = size(frame1);

%% 简化的特征点选取
fprintf('请选择3个固定参考点（如地面、山体等静止区域）\n');
figure('Name', '选择参考点', 'NumberTitle', 'off');
imshow(frame1);
title('点击3个固定不动的参考点');
hold on;

refPoints = zeros(3, 2);
for i = 1:3
    fprintf('点击第%d个参考点\n', i);
    [x, y] = ginput(1);
    refPoints(i, :) = [round(x), round(y)];
    plot(x, y, 'ro', 'MarkerSize', 10, 'LineWidth', 2);
    text(x+10, y, sprintf('参考点%d', i), 'Color', 'r', 'FontSize', 12);
end

fprintf('请选择1个桥梁测量点\n');
[x, y] = ginput(1);
structPoint = [round(x), round(y)];
plot(x, y, 'bs', 'MarkerSize', 10, 'LineWidth', 2);
text(x+10, y, '测量点', 'Color', 'b', 'FontSize', 12);
close(gcf);

%% 简化的尺度标定
fprintf('请输入像素到毫米的转换因子（如果不知道，输入1）: ');
scaleFactor = input('');
if isempty(scaleFactor) || scaleFactor <= 0
    scaleFactor = 1;
end

%% 处理图像序列
fprintf('开始处理图像...\n');
templateSize = 20; % 简化为单一模板尺寸
searchRange = 100;

% 初始化
refDisplacements = zeros(numFrames, 3, 2); % 参考点位移
structDisplacements = zeros(numFrames, 2); % 结构点位移
globalMotion = zeros(numFrames, 2); % 全局运动
correctedImages = cell(numFrames, 1);
originalImages = cell(numFrames, 1);

% 提取参考模板
refTemplates = cell(3, 1);
grayFrame1 = rgb2gray(frame1);
for i = 1:3
    x = refPoints(i, 1); y = refPoints(i, 2);
    refTemplates{i} = imcrop(grayFrame1, [x-templateSize, y-templateSize, 2*templateSize, 2*templateSize]);
end

% 提取结构点模板
structTemplate = imcrop(grayFrame1, [structPoint(1)-templateSize, structPoint(2)-templateSize, 2*templateSize, 2*templateSize]);

% 逐帧处理
for frameIdx = 1:numFrames
    fprintf('处理第 %d/%d 帧\n', frameIdx, numFrames);
    
    % 读取当前帧
    currentImage = imread(fullfile(imageFolder, imageFiles(frameIdx).name));
    if size(currentImage,3) == 1
        currentImage = cat(3, currentImage, currentImage, currentImage);
    end
    originalImages{frameIdx} = currentImage;
    grayFrame = rgb2gray(currentImage);
    
    if frameIdx == 1
        % 第一帧作为参考
        globalMotion(1, :) = [0, 0];
        correctedImages{1} = currentImage;
        continue;
    end
    
    % 跟踪参考点
    currentRefDisp = zeros(3, 2);
    for i = 1:3
        % 使用模板匹配
        corrMap = normxcorr2(refTemplates{i}, grayFrame);
        [~, maxIdx] = max(corrMap(:));
        [yPeak, xPeak] = ind2sub(size(corrMap), maxIdx);
        
        % 转换为实际坐标
        x_new = xPeak - templateSize;
        y_new = yPeak - templateSize;
        
        % 计算位移（物理单位）
        dispX = (x_new - refPoints(i, 1)) * scaleFactor;
        dispY = (y_new - refPoints(i, 2)) * scaleFactor;
        
        currentRefDisp(i, :) = [dispX, dispY];
        refDisplacements(frameIdx, i, :) = [dispX, dispY];
    end
    
    % 估计全局运动（参考点位移的平均值）
    globalMotion(frameIdx, :) = mean(currentRefDisp, 1);
    
    % 修正当前图像
    dx = globalMotion(frameIdx, 1) / scaleFactor; % 转换为像素
    dy = globalMotion(frameIdx, 2) / scaleFactor;
    
    % 创建变换矩阵
    tform = affine2d([1 0 0; 0 1 0; -dx -dy 1]);
    correctedImages{frameIdx} = imwarp(currentImage, tform, 'OutputView', imref2d([frameH frameW]));
    
    % 跟踪结构点
    corrMap = normxcorr2(structTemplate, grayFrame);
    [~, maxIdx] = max(corrMap(:));
    [yPeak, xPeak] = ind2sub(size(corrMap), maxIdx);
    
    x_struct = xPeak - templateSize;
    y_struct = yPeak - templateSize;
    
    % 计算结构点原始位移
    originalDispX = (x_struct - structPoint(1)) * scaleFactor;
    originalDispY = (y_struct - structPoint(2)) * scaleFactor;
    
    % 计算消除晃动后的位移
    correctedDispX = originalDispX - globalMotion(frameIdx, 1);
    correctedDispY = originalDispY - globalMotion(frameIdx, 2);
    
    structDisplacements(frameIdx, :) = [correctedDispX, correctedDispY];
end

%% 生成对比结果
fprintf('生成对比结果...\n');

% 绘制位移曲线对比
figure('Position', [100, 100, 1200, 800], 'Name', '晃动修正效果对比');

subplot(3,1,1);
plot(1:numFrames, globalMotion(:,1), 'r-', 'LineWidth', 2);
hold on;
plot(1:numFrames, globalMotion(:,2), 'b-', 'LineWidth', 2);
grid on;
legend('X方向晃动', 'Y方向晃动');
title('检测到的无人机晃动');
ylabel('位移 (mm)');

subplot(3,1,2);
% 计算结构点修正前的位移（包含晃动）
originalStructDisp = structDisplacements + globalMotion;
plot(1:numFrames, originalStructDisp(:,1), 'r--', 'LineWidth', 1.5);
hold on;
plot(1:numFrames, originalStructDisp(:,2), 'b--', 'LineWidth', 1.5);
grid on;
legend('X方向（修正前）', 'Y方向（修正前）');
title('结构点位移 - 修正前（包含晃动）');
ylabel('位移 (mm)');

subplot(3,1,3);
plot(1:numFrames, structDisplacements(:,1), 'r-', 'LineWidth', 2);
hold on;
plot(1:numFrames, structDisplacements(:,2), 'b-', 'LineWidth', 2);
grid on;
legend('X方向（修正后）', 'Y方向（修正后）');
title('结构点位移 - 修正后（消除晃动）');
xlabel('图像序号');
ylabel('位移 (mm)');

% 保存图像序列
fprintf('保存修正后的图像...\n');
for i = 1:numFrames
    imwrite(originalImages{i}, fullfile(savePath, sprintf('original_%03d.jpg', i)));
    imwrite(correctedImages{i}, fullfile(savePath, sprintf('corrected_%03d.jpg', i)));
end

% 保存数据
save(fullfile(savePath, 'verification_results.mat'), ...
    'globalMotion', 'structDisplacements', 'originalImages', 'correctedImages', ...
    'scaleFactor', 'refPoints', 'structPoint');

%% 显示统计结果
fprintf('\n=== 晃动修正效果统计 ===\n');
fprintf('检测到的晃动范围:\n');
fprintf('  X方向: %.2f ~ %.2f mm (标准差: %.2f mm)\n', ...
    min(globalMotion(:,1)), max(globalMotion(:,1)), std(globalMotion(:,1)));
fprintf('  Y方向: %.2f ~ %.2f mm (标准差: %.2f mm)\n', ...
    min(globalMotion(:,2)), max(globalMotion(:,2)), std(globalMotion(:,2)));

fprintf('\n结构点位移测量:\n');
originalStdX = std(originalStructDisp(:,1));
originalStdY = std(originalStructDisp(:,2));
correctedStdX = std(structDisplacements(:,1));
correctedStdY = std(structDisplacements(:,2));

fprintf('  修正前标准差: X=%.2f mm, Y=%.2f mm\n', originalStdX, originalStdY);
fprintf('  修正后标准差: X=%.2f mm, Y=%.2f mm\n', correctedStdX, correctedStdY);
fprintf('  稳定性改善: X方向%.1f%%, Y方向%.1f%%\n', ...
    (originalStdX-correctedStdX)/originalStdX*100, (originalStdY-correctedStdY)/originalStdY*100);

fprintf('\n修正后的图像和数据已保存到: %s\n', savePath);
fprintf('程序执行完成！\n');
