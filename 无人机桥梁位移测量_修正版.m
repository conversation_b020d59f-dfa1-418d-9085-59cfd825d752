% 无人机桥梁位移测量程序（修正版：解决图像晃动消除问题）
% 核心修正：修复累积位移计算逻辑，统一图像修正参考系
clear all; close all; clc;

%% 参数设置
imageFolder = '.'; % 当前目录下的图像文件
savePath = fullfile(imageFolder, 'results_corrected'); 
if ~exist(savePath, 'dir'), mkdir(savePath); end 
imageExt = '*.JPG'; % 修改为JPG格式
isCalibrated = false; 
scaleFactor = 1; 
frameRate = 30; 
unit = '毫米'; 
numFeaturePoints = 3;  % 外部特征点数量
numStructurePoints = 2;  % 桥梁结构点数量
templateSize = [15, 20, 25, 30];  % 多尺度模板
searchRange = 120;  % 搜索范围
corrThreshold = 0.25;  % 相关系数阈值
dynamicThreshold = true; 
ransacThreshold = 2.5;  
maxRANSACIter = 1000;  
templateUpdateRate = 0.3;  
updateInterval = 2;  
maxFailCount = 6;  
motionSmoothing = 0.2;  
driftCorrectionInterval = 15;  
displacementSmoothingWindow = 3;  
saveEveryFrame = true; 

%% 读取图像序列
try
    imageFiles = dir(fullfile(imageFolder, imageExt));
    numFrames = length(imageFiles);
    if numFrames < 2
        error('图像数量过少（至少需要2张）');
    end
    [~, idx] = sort([imageFiles.datenum]);
    imageFiles = imageFiles(idx);  % 按时间排序
    
    fprintf('成功读取图像序列: %s\n', imageFolder);
    fprintf('图像总数: %d 张\n', numFrames);
    fprintf('帧率: %.2f FPS\n', frameRate);
    
    frame1 = imread(fullfile(imageFolder, imageFiles(1).name));
    if size(frame1,3) == 1
        frame1 = cat(3, frame1, frame1, frame1);  % 转为RGB
    end
    [frameH, frameW, ~] = size(frame1);
    grayFrame1 = rgb2gray(frame1);
catch ME
    fprintf('错误: 无法读取图像序列 %s\n', imageFolder);
    fprintf('详情: %s\n', ME.message);
    return;
end

%% 生成颜色标记
colorNames = {'r', 'g', 'b', 'c', 'm', 'y', 'k'}; 
colorIdx = 1:(numFeaturePoints + numStructurePoints);
colorNames = colorNames(mod(colorIdx-1, length(colorNames)) + 1);

%% 尺度标定（简化版）
if ~isCalibrated
    fprintf('\n=== 尺度标定 ===\n');
    figure('Name','尺度标定','NumberTitle','off');
    imshow(frame1);
    title('点击水平方向已知长度物体的两个端点');
    hold on;
    
    try
        confirmed = false;
        while ~confirmed
            [x1, y1] = ginput(1);
            x1 = round(x1); y1 = round(y1);
            plot(x1, y1, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', 'r');
            
            [x2, y2] = ginput(1);
            x2 = round(x2); y2 = round(y2);
            plot(x2, y2, 'o', 'MarkerSize', 8, 'LineWidth', 2, 'Color', 'r');
            line([x1,x2], [y1,y2], 'Color', 'r', 'LineWidth', 2);
            
            pixelDist = sqrt((x2-x1)^2 + (y2-y1)^2);
            if pixelDist < 250 
                warning('标定距离过短，请重新选取');
                clf; imshow(frame1); hold on;
                continue;
            end
            
            realDist = input('这两点的实际距离(毫米): ');
            if realDist <= 0
                error('实际距离必须为正数');
            end
            scaleFactor = realDist / pixelDist;  % 毫米/像素
            
            fprintf('尺度因子: %.6f 毫米/像素\n', scaleFactor);
            resp = input('是否确认此尺度？(y/n): ', 's');
            if strcmpi(resp, 'y')
                confirmed = true;
            else
                clf; imshow(frame1); hold on;
            end
        end
        
        isCalibrated = true;
        close(gcf);
    catch ME
        fprintf('标定失败: %s\n', ME.message);
        fprintf('使用默认尺度（1像素=1%s）\n', unit);
    end
end

%% 选取外部特征点
fprintf('\n=== 选取外部特征点（%d个） ===\n', numFeaturePoints);
figure('Name','外部特征点选取','NumberTitle','off');
imshow(frame1);
title('选取固定不动的参考点（如地面、山体）');
hold on;
featPoints = zeros(numFeaturePoints, 2);  % 初始位置
featCurrentPos = zeros(numFeaturePoints, 2);  % 当前位置
featTemplates = cell(numFeaturePoints, length(templateSize));  % 模板
featTextureScore = zeros(numFeaturePoints, 1);  % 纹理评分
featReliability = ones(numFeaturePoints, 1);  % 可靠性

for k = 1:numFeaturePoints
    text(10,20+20*k, sprintf('请点击第%d个外部特征点', k), ...
        'Color', colorNames{k}, 'FontSize',12,'BackgroundColor','white');
    while true
        [x, y] = ginput(1);
        x = round(x); y = round(y);
        margin = max(templateSize) + 8;
        if x < margin || x > frameW - margin || ...
           y < margin || y > frameH - margin
            warning('特征点%d太靠近边界，请重新选取', k);
            continue;
        end
        % 纹理检查
        ts = max(templateSize);
        temp = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
        [gx, gy] = imgradient(temp);
        gradMag = sqrt(gx.^2 + gy.^2);
        textureScore = var(gradMag(:));
        if textureScore < 400
            warning('特征点%d纹理不足（评分:%.1f），请重新选取', k, textureScore);
            continue;
        end
        featTextureScore(k) = textureScore;
        break;
    end
    featPoints(k,:) = [x, y];
    featCurrentPos(k,:) = [x, y];
    % 保存多尺度模板
    for s = 1:length(templateSize)
        ts = templateSize(s);
        featTemplates{k,s} = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
    end
    plot(x, y, 'o', 'MarkerSize', 10, 'LineWidth', 2, 'Color', colorNames{k});
    text(x+5, y+5, sprintf('特征点%d', k), 'Color', colorNames{k}, 'FontSize',10);
end
pause(1);
close(gcf);

%% 选取结构点
fprintf('\n=== 选取结构点（%d个） ===\n', numStructurePoints);
figure('Name','结构点选取','NumberTitle','off');
imshow(frame1);
title('选取桥梁上的测量点');
hold on;
strucPoints = zeros(numStructurePoints, 2);  % 初始位置
strucCurrentPos = zeros(numStructurePoints, 2);  % 当前位置
strucTemplates = cell(numStructurePoints, length(templateSize));  % 模板
strucPrevPos = cell(numStructurePoints, 5);  % 历史位置
strucTextureScore = zeros(numStructurePoints, 1);  % 纹理评分

for k = 1:numStructurePoints
    text(10,20+20*k, sprintf('请点击第%d个结构点', k), ...
        'Color', colorNames{numFeaturePoints + k}, 'FontSize',12,'BackgroundColor','white');
    while true
        [x, y] = ginput(1);
        x = round(x); y = round(y);
        margin = max(templateSize) + 8;
        if x < margin || x > frameW - margin || ...
           y < margin || y > frameH - margin
            warning('结构点%d太靠近边界，请重新选取', k);
            continue;
        end
        % 纹理检查
        ts = max(templateSize);
        temp = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
        [gx, gy] = imgradient(temp);
        gradMag = sqrt(gx.^2 + gy.^2);
        textureScore = var(gradMag(:));
        if textureScore < 600
            warning('结构点%d纹理不足（评分:%.1f），请重新选取', k, textureScore);
            continue;
        end
        strucTextureScore(k) = textureScore;
        break;
    end
    strucPoints(k,:) = [x, y];
    strucCurrentPos(k,:) = [x, y];
    for p = 1:5
        strucPrevPos{k,p} = [x, y];
    end
    % 保存多尺度模板
    for s = 1:length(templateSize)
        ts = templateSize(s);
        strucTemplates{k,s} = imcrop(grayFrame1, [x-ts, y-ts, 2*ts, 2*ts]);
    end
    plot(x, y, 's', 'MarkerSize', 10, 'LineWidth', 2, 'Color', colorNames{numFeaturePoints + k});
    text(x+5, y+5, sprintf('结构点%d', k), 'Color', colorNames{numFeaturePoints + k}, 'FontSize',10);
end
pause(1);
close(gcf);

%% 初始化变量（修正版）
featFrameDisplacement = zeros(numFrames, numFeaturePoints, 2);  % 特征点绝对位移（相对第一帧）
globalMotion = zeros(numFrames, 2);  % 全局运动（绝对位移，相对第一帧）
globalMotionSmoothed = zeros(numFrames, 2);  % 平滑后的全局运动
correctedImages = cell(numFrames, 1);  % 修正后的图像
markedImages = cell(numFrames, 1);  % 标记图像
featFailCount = zeros(1, numFeaturePoints);
featReliability = ones(numFeaturePoints, 1);

% 结构点相关变量
strucFrameDisplacement = zeros(numFrames, numStructurePoints, 2);  % 结构点原始位移
absoluteDisplacement = zeros(numFrames, numStructurePoints, 2);  % 消除晃动后的位移
absoluteDisplacementSmoothed = zeros(numFrames, numStructurePoints, 2);  % 平滑后位移
strucFailCount = zeros(1, numStructurePoints);
prevCorrVals = zeros(numStructurePoints, 5);
velocityX = zeros(numStructurePoints, 1);
velocityY = zeros(numStructurePoints, 1);
maxVelocity = 200;
accelerationLimit = 60;

%% 多尺度匹配函数（优化版）
function [bestX, bestY, bestCorr] = multiScaleMatching(grayFrame, prevPos, templates, searchRange, templateSize)
    bestCorr = 0;
    bestX = prevPos(1);
    bestY = prevPos(2);
    x0 = prevPos(1);
    y0 = prevPos(2);

    % 定义搜索区域
    searchROI = [max(1, x0 - searchRange), max(1, y0 - searchRange), ...
        min(2*searchRange, size(grayFrame,2) - (x0 - searchRange)), ...
        min(2*searchRange, size(grayFrame,1) - (y0 - searchRange))];
    searchArea = imcrop(grayFrame, searchROI);
    if isempty(searchArea), return; end
    searchArea = double(searchArea);

    % 多尺度模板匹配
    for s = 1:length(templateSize)
        template = templates{s};
        if isempty(template), continue; end
        template = double(template);
        ts = size(template,1);
        if size(template,1) ~= size(template,2) || ts > size(searchArea,1)
            continue;
        end

        % 基于梯度的匹配
        [tGradX, tGradY] = imgradient(template);
        templateNorm = sqrt(tGradX.^2 + tGradY.^2);
        templateNorm = templateNorm - mean(templateNorm(:));
        templateNorm = templateNorm / (norm(templateNorm(:)) + eps);

        [sGradX, sGradY] = imgradient(searchArea);
        searchNorm = sqrt(sGradX.^2 + sGradY.^2);
        searchNorm = searchNorm - mean(searchNorm(:));
        searchNorm = searchNorm / (norm(searchNorm(:)) + eps);

        corrMap = normxcorr2(templateNorm, searchNorm);
        corrMap = corrMap(ts:end-ts+1, ts:end-ts+1);

        [maxVal, maxIdx] = max(corrMap(:));
        [yIdx, xIdx] = ind2sub(size(corrMap), maxIdx);

        % 转换回原始图像坐标
        x_i = searchROI(1) + xIdx - 1;
        y_i = searchROI(2) + yIdx - 1;
        x_i = max(1, min(x_i, size(grayFrame,2)));
        y_i = max(1, min(y_i, size(grayFrame,1)));

        if maxVal > bestCorr + 0.005
            bestCorr = maxVal;
            bestX = x_i;
            bestY = y_i;
        end
    end
end

%% RANSAC全局运动估计函数（修正版）
function [avgDisp, inliers, reliability] = ransacMotionEstimation(displacements, reliability, threshold, maxIter)
    N = size(displacements, 1);
    if N < 3
        avgDisp = mean(displacements);
        inliers = 1:N;
        reliability = ones(1,N);
        return;
    end

    bestModel = [0, 0];
    bestCount = 0;
    weights = reliability / sum(reliability);

    for iter = 1:maxIter
        idx = randsample(N, 3, true, weights);
        sample = displacements(idx, :);
        model = sum(sample .* weights(idx)') / sum(weights(idx));  % 加权平均
        errors = sqrt(sum((displacements - repmat(model, N, 1)).^2, 2));
        inliers = errors < threshold;
        currentCount = sum(inliers .* reliability');

        if currentCount > bestCount
            bestCount = currentCount;
            bestModel = model;
        end
    end

    % 重新计算最终模型
    errors = sqrt(sum((displacements - repmat(bestModel, N, 1)).^2, 2));
    inliers = errors < threshold * 1.5;
    avgDisp = sum(displacements(inliers,:) .* weights(inliers)') / sum(weights(inliers));
    reliability = max(0.1, 1 - errors / (max(errors) + eps));
end

%% 图像刚体位移修正函数（核心修正）
function correctedImg = correctRigidMotion(img, absoluteDx, absoluteDy)
    % absoluteDx/absoluteDy: 相对于第一帧的绝对像素位移
    [h, w, c] = size(img);
    % 创建仿射变换矩阵：反向补偿绝对位移，将图像对齐到第一帧
    tform = affine2d([1 0 0; 0 1 0; -absoluteDx -absoluteDy 1]);
    % 使用高质量插值
    correctedImg = imwarp(img, tform, 'OutputView', imref2d([h w]), ...
        'Interp', 'bicubic', 'FillValues', [128 128 128]);
end

%% 处理每帧图像（核心修正版）
fprintf('\n开始处理图像序列...\n');
hWait = waitbar(0, '处理中...');
dispHistoryX = cell(numStructurePoints, 1);
dispHistoryY = cell(numStructurePoints, 1);
for k = 1:numStructurePoints
    dispHistoryX{k} = zeros(1, displacementSmoothingWindow);
    dispHistoryY{k} = zeros(1, displacementSmoothingWindow);
end

for i = 1:numFrames
    waitbar(i/numFrames, hWait, sprintf('处理第 %d/%d 张...', i, numFrames));
    currentImage = imread(fullfile(imageFolder, imageFiles(i).name));
    if size(currentImage,3) == 1
        currentImage = cat(3, currentImage, currentImage, currentImage);
    end
    grayFrame = rgb2gray(currentImage);
    markedFrame = currentImage;  % 用于标记的原始图像

    %% 步骤1：跟踪外部特征点，估计全局运动
    for k = 1:numFeaturePoints
        prevPos = featCurrentPos(k,:);

        if i == 1
            % 第一帧：初始化
            featFrameDisplacement(i,k,:) = [0, 0];  % 第一帧位移为0
            markedFrame = insertMarker(markedFrame, prevPos, 'o', 'Color', colorNames{k});
            continue;
        end

        % 多尺度匹配跟踪
        [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, prevPos, featTemplates(k,:), searchRange, templateSize);

        try
            if corrVal < corrThreshold * 0.7
                error(sprintf('相关系数过低（%.2f）', corrVal));
            end

            % 计算相对于第一帧的绝对位移（物理单位）
            absDispX = (x_i - featPoints(k,1)) * scaleFactor;
            absDispY = (y_i - featPoints(k,2)) * scaleFactor;

            % 异常值检测和修正
            if i > 1
                maxFeatDisp = 40;  % 最大允许单帧变化
                prevDispX = featFrameDisplacement(i-1,k,1);
                prevDispY = featFrameDisplacement(i-1,k,2);

                if abs(absDispX - prevDispX) > maxFeatDisp
                    warning('特征点%d X方向位移异常，已修正', k);
                    absDispX = prevDispX + sign(absDispX - prevDispX) * maxFeatDisp * 0.6;
                    x_i = featPoints(k,1) + absDispX / scaleFactor;
                end
                if abs(absDispY - prevDispY) > maxFeatDisp
                    warning('特征点%d Y方向位移异常，已修正', k);
                    absDispY = prevDispY + sign(absDispY - prevDispY) * maxFeatDisp * 0.6;
                    y_i = featPoints(k,2) + absDispY / scaleFactor;
                end
            end

            % 存储绝对位移
            featFrameDisplacement(i,k,:) = [absDispX, absDispY];

            % 平滑更新当前位置
            smoothedX = motionSmoothing * x_i + (1 - motionSmoothing) * prevPos(1);
            smoothedY = motionSmoothing * y_i + (1 - motionSmoothing) * prevPos(2);
            featCurrentPos(k,:) = [max(1, min(smoothedX, frameW)), max(1, min(smoothedY, frameH))];

            % 模板更新
            if mod(i, updateInterval) == 0 && corrVal > corrThreshold
                for s = 1:length(templateSize)
                    ts = templateSize(s);
                    if x_i-ts > 0 && y_i-ts > 0 && x_i+ts <= frameW && y_i+ts <= frameH
                        newTemplate = imcrop(grayFrame, [x_i-ts, y_i-ts, 2*ts, 2*ts]);
                        if size(newTemplate,1) == 2*ts && size(newTemplate,2) == 2*ts
                            featTemplates{k,s} = uint8(templateUpdateRate * double(newTemplate) + ...
                                (1 - templateUpdateRate) * double(featTemplates{k,s}));
                        end
                    end
                end
            end

            markedFrame = insertMarker(markedFrame, [x_i, y_i], 'o', 'Color', colorNames{k});
            featFailCount(k) = 0;
            featReliability(k) = min(1, featReliability(k) + 0.08);

        catch ME
            % 跟踪失败处理
            featFailCount(k) = featFailCount(k) + 1;
            fprintf('第%d张图像特征点%d跟踪失败: %s\n', i, k, ME.message);
            featReliability(k) = max(0.1, featReliability(k) - 0.1);

            if featFailCount(k) >= maxFailCount
                % 重定位
                [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, featPoints(k,:), featTemplates(k,:), searchRange*3, templateSize);
                featCurrentPos(k,:) = [x_i, y_i];
                absDispX = (x_i - featPoints(k,1)) * scaleFactor;
                absDispY = (y_i - featPoints(k,2)) * scaleFactor;
                featFrameDisplacement(i,k,:) = [absDispX, absDispY];
                featFailCount(k) = 0;
                featReliability(k) = 0.8;
                fprintf('特征点%d重定位成功\n', k);
            else
                % 使用前一帧位移
                if i > 1
                    featFrameDisplacement(i,k,:) = featFrameDisplacement(i-1,k,:);
                else
                    featFrameDisplacement(i,k,:) = [0, 0];
                end
            end

            markedFrame = insertMarker(markedFrame, featCurrentPos(k,:), 'o', 'Color', 'k');
        end
    end

    %% 步骤2：估计全局运动（修正版：直接使用绝对位移）
    if i == 1
        globalMotion(i,:) = [0, 0];  % 第一帧全局运动为0
        globalMotionSmoothed(i,:) = [0, 0];
    else
        % 筛选可靠特征点
        reliableIdx = featReliability > 0.3;
        if sum(reliableIdx) < min(2, numFeaturePoints*0.5)
            warning('可靠特征点不足，使用全部特征点');
            dispMat = squeeze(featFrameDisplacement(i,:,:));  % 当前帧的绝对位移
            [globalMotion_vec, ~, ~] = ransacMotionEstimation(dispMat, ones(1,numFeaturePoints), 3.0, 600);
        else
            dispMat = squeeze(featFrameDisplacement(i,reliableIdx,:));  % 可靠特征点的绝对位移
            [globalMotion_vec, ~, featReliability(reliableIdx)] = ransacMotionEstimation(...
                dispMat, featReliability(reliableIdx), ransacThreshold, maxRANSACIter);
        end

        % globalMotion存储的是相对于第一帧的绝对位移
        globalMotion(i,:) = globalMotion_vec;

        % 平滑全局运动
        windowSize = min(7, i);
        globalMotionSmoothed(1:i,:) = movmean(globalMotion(1:i,:), windowSize, 1);

        % 漂移校正（可选）
        if mod(i, driftCorrectionInterval) == 0 && i > driftCorrectionInterval
            % 计算特征点的平均漂移
            avgDrift = mean(featCurrentPos - featPoints, 1) * scaleFactor;
            maxDrift = 15;
            avgDrift = sign(avgDrift) .* min(abs(avgDrift), maxDrift);

            % 应用漂移校正
            globalMotionSmoothed(i,:) = globalMotionSmoothed(i,:) - avgDrift * 0.3;
            fprintf('第%d帧漂移校正: X-%.2f%s, Y-%.2f%s\n', i, avgDrift(1), unit, avgDrift(2), unit);
        end
    end

    %% 步骤3：修正当前图像（核心修正：使用绝对位移）
    if i == 1
        correctedFrame = currentImage;  % 第一帧不需要修正
    else
        % 将物理位移转换为像素位移
        absoluteDx = globalMotionSmoothed(i,1) / scaleFactor;  % X方向绝对像素位移
        absoluteDy = globalMotionSmoothed(i,2) / scaleFactor;  % Y方向绝对像素位移

        % 应用刚体位移修正，将当前图像对齐到第一帧坐标系
        correctedFrame = correctRigidMotion(currentImage, absoluteDx, absoluteDy);
    end

    %% 步骤4：跟踪结构点，测量桥梁位移
    for k = 1:numStructurePoints
        if i == 1
            % 第一帧初始化
            strucFrameDisplacement(i,k,:) = [0, 0];
            absoluteDisplacement(i,k,:) = [0, 0];
            absoluteDisplacementSmoothed(i,k,:) = [0, 0];
            markedFrame = insertMarker(markedFrame, strucCurrentPos(k,:), 's', 'Color', colorNames{numFeaturePoints + k});
            correctedFrame = insertMarker(correctedFrame, strucCurrentPos(k,:), 's', 'Color', colorNames{numFeaturePoints + k});
            prevCorrVals(k,:) = 0.5;
            continue;
        end

        % 预测位置（基于历史运动）
        prevPositions = cell2mat(strucPrevPos(k,:));
        if size(prevPositions, 1) >= 2
            velX = mean(diff(prevPositions(:,1)));
            velY = mean(diff(prevPositions(:,2)));
            predX = strucCurrentPos(k,1) + velX * 0.7;
            predY = strucCurrentPos(k,2) + velY * 0.7;
        else
            predX = strucCurrentPos(k,1);
            predY = strucCurrentPos(k,2);
        end
        predX = max(1, min(round(predX), frameW));
        predY = max(1, min(round(predY), frameH));
        predPos = [predX, predY];

        % 跟踪结构点
        [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, predPos, strucTemplates(k,:), searchRange, templateSize);

        % 动态调整相关系数阈值
        prevCorrVals(k,2:end) = prevCorrVals(k,1:end-1);
        prevCorrVals(k,1) = corrVal;
        meanPrevCorr = mean(prevCorrVals(k,:));
        currentThreshold = max(corrThreshold * 0.6, min(meanPrevCorr * 0.5, 0.25));

        try
            if corrVal < currentThreshold
                % 二次搜索
                [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, strucCurrentPos(k,:), strucTemplates(k,:), searchRange*2, templateSize);
                if corrVal < currentThreshold * 0.9
                    error(sprintf('相关系数过低（%.2f）', corrVal));
                end
            end

            % 计算结构点相对于第一帧的绝对位移（物理单位）
            absDispX = (x_i - strucPoints(k,1)) * scaleFactor;
            absDispY = (y_i - strucPoints(k,2)) * scaleFactor;
            strucFrameDisplacement(i,k,:) = [absDispX, absDispY];

            % 计算消除无人机晃动后的真实桥梁位移
            % 关键修正：结构点绝对位移 - 全局运动 = 桥梁真实位移
            realDispX = absDispX - globalMotionSmoothed(i,1);
            realDispY = absDispY - globalMotionSmoothed(i,2);

            % 平滑处理
            dispHistoryX{k} = [dispHistoryX{k}(2:end), realDispX];
            dispHistoryY{k} = [dispHistoryY{k}(2:end), realDispY];

            if i <= displacementSmoothingWindow
                smoothedDispX = mean(dispHistoryX{k}(1:i));
                smoothedDispY = mean(dispHistoryY{k}(1:i));
            else
                smoothedDispX = mean(dispHistoryX{k}) * 0.8 + absoluteDisplacementSmoothed(i-1,k,1) * 0.2;
                smoothedDispY = mean(dispHistoryY{k}) * 0.8 + absoluteDisplacementSmoothed(i-1,k,2) * 0.2;
            end

            absoluteDisplacement(i,k,:) = [realDispX, realDispY];
            absoluteDisplacementSmoothed(i,k,:) = [smoothedDispX, smoothedDispY];

            % 更新历史位置
            for p = 5:-1:2
                strucPrevPos{k,p} = strucPrevPos{k,p-1};
            end
            strucPrevPos{k,1} = [x_i, y_i];
            strucCurrentPos(k,:) = [x_i, y_i];

            % 模板更新
            if mod(i, updateInterval) == 0 && corrVal > currentThreshold
                for s = 1:length(templateSize)
                    ts = templateSize(s);
                    if x_i-ts > 0 && y_i-ts > 0 && x_i+ts <= frameW && y_i+ts <= frameH
                        newTemplate = imcrop(grayFrame, [x_i-ts, y_i-ts, 2*ts, 2*ts]);
                        if size(newTemplate,1) == 2*ts && size(newTemplate,2) == 2*ts
                            strucTemplates{k,s} = uint8(templateUpdateRate * double(newTemplate) + ...
                                (1 - templateUpdateRate) * double(strucTemplates{k,s}));
                        end
                    end
                end
            end

            % 标记结构点
            markedFrame = insertMarker(markedFrame, [x_i, y_i], 's', 'Color', colorNames{numFeaturePoints + k});

            % 在修正后的图像上标记（需要考虑图像修正的位移）
            correctedX = x_i - globalMotionSmoothed(i,1) / scaleFactor;
            correctedY = y_i - globalMotionSmoothed(i,2) / scaleFactor;
            if correctedX > 0 && correctedY > 0 && correctedX <= frameW && correctedY <= frameH
                correctedFrame = insertMarker(correctedFrame, [correctedX, correctedY], 's', 'Color', colorNames{numFeaturePoints + k});
            end

            % 显示位移信息
            dispText = sprintf('结构点%d: X=%.1f, Y=%.1f%s', k, smoothedDispX, smoothedDispY, unit);
            markedFrame = insertText(markedFrame, [x_i+15, y_i], dispText, ...
                'TextColor', colorNames{numFeaturePoints + k}, 'FontSize', 10);

            strucFailCount(k) = 0;

        catch ME
            % 结构点跟踪失败处理
            strucFailCount(k) = strucFailCount(k) + 1;
            fprintf('第%d张图像结构点%d跟踪失败: %s\n', i, k, ME.message);

            if strucFailCount(k) >= maxFailCount
                % 重定位
                [x_i, y_i, corrVal] = multiScaleMatching(grayFrame, strucPoints(k,:), strucTemplates(k,:), searchRange*4, templateSize);
                strucCurrentPos(k,:) = [x_i, y_i];
                for p = 1:5
                    strucPrevPos{k,p} = [x_i, y_i];
                end

                absDispX = (x_i - strucPoints(k,1)) * scaleFactor;
                absDispY = (y_i - strucPoints(k,2)) * scaleFactor;
                strucFrameDisplacement(i,k,:) = [absDispX, absDispY];
                realDispX = absDispX - globalMotionSmoothed(i,1);
                realDispY = absDispY - globalMotionSmoothed(i,2);
                absoluteDisplacement(i,k,:) = [realDispX, realDispY];
                absoluteDisplacementSmoothed(i,k,:) = 0.7*absoluteDisplacementSmoothed(i-1,k,:) + 0.3*[realDispX, realDispY];

                strucFailCount(k) = 0;
                fprintf('结构点%d重定位成功\n', k);
            else
                % 使用预测值
                if i > 1
                    absoluteDisplacementSmoothed(i,k,:) = absoluteDisplacementSmoothed(i-1,k,:);
                else
                    absoluteDisplacementSmoothed(i,k,:) = [0, 0];
                end
            end

            markedFrame = insertMarker(markedFrame, strucCurrentPos(k,:), 's', 'Color', 'k');
            correctedFrame = insertMarker(correctedFrame, strucCurrentPos(k,:), 's', 'Color', 'k');
        end
    end

    % 保存图像
    markedImages{i} = markedFrame;
    correctedImages{i} = correctedFrame;
    if saveEveryFrame
        imwrite(markedFrame, fullfile(savePath, sprintf('marked_frame_%d.jpg', i)));
        imwrite(correctedFrame, fullfile(savePath, sprintf('corrected_frame_%d.jpg', i)));
    end
end
close(hWait);
fprintf('图像处理完成! 修正后图像已保存至: %s\n', savePath);

%% 结果分析和可视化
% 计算最大位移
maxDispX = zeros(numStructurePoints, 1);
maxDispY = zeros(numStructurePoints, 1);
maxIdxX = zeros(numStructurePoints, 1);
maxIdxY = zeros(numStructurePoints, 1);

for k = 1:numStructurePoints
    dispX = absoluteDisplacementSmoothed(:,k,1);
    dispY = absoluteDisplacementSmoothed(:,k,2);

    % 异常值处理
    windowSize = min(7, length(dispX)-1);
    if windowSize > 0
        [~, outliersX] = isoutlier(dispX, 'movmedian', windowSize, 'ThresholdFactor', 3.0);
        [~, outliersY] = isoutlier(dispY, 'movmedian', windowSize, 'ThresholdFactor', 3.0);

        if any(outliersX), dispX(outliersX) = NaN; end
        if any(outliersY), dispY(outliersY) = NaN; end

        dispX = fillmissing(dispX, 'movmean', 5);
        dispY = fillmissing(dispY, 'movmean', 5);
    end

    [maxDispX(k), maxIdxX(k)] = max(abs(dispX));
    [maxDispY(k), maxIdxY(k)] = max(abs(dispY));
end

% 频谱分析
fs = frameRate;
L = numFrames;
dominantFreqX = zeros(numStructurePoints, 1);
dominantFreqY = zeros(numStructurePoints, 1);

for k = 1:numStructurePoints
    % X方向频谱
    dispX = absoluteDisplacementSmoothed(:,k,1);
    dispX = dispX - movmean(dispX, min(5, L-1));
    N_fft = max(2^nextpow2(L), 64);
    YX = fft(dispX, N_fft);
    P2X = abs(YX / L);
    P1X = P2X(1:floor(N_fft/2)+1);
    P1X(2:end-1) = 2*P1X(2:end-1);
    f_k = fs*(0:length(P1X)-1)/N_fft;
    validFreqIdx = f_k >= 0.05 & f_k <= 10;

    if sum(validFreqIdx) > 1
        [~, freqIdX] = max(P1X(validFreqIdx));
        validFreqs = f_k(validFreqIdx);
        dominantFreqX(k) = validFreqs(freqIdX);
    else
        [~, freqIdX] = max(P1X(2:end));
        dominantFreqX(k) = f_k(freqIdX+1);
    end

    % Y方向频谱
    dispY = absoluteDisplacementSmoothed(:,k,2);
    dispY = dispY - movmean(dispY, min(5, L-1));
    YY = fft(dispY, N_fft);
    P2Y = abs(YY / L);
    P1Y = P2Y(1:floor(N_fft/2)+1);
    P1Y(2:end-1) = 2*P1Y(2:end-1);

    if sum(validFreqIdx) > 1
        [~, freqIdY] = max(P1Y(validFreqIdx));
        dominantFreqY(k) = validFreqs(freqIdY);
    else
        [~, freqIdY] = max(P1Y(2:end));
        dominantFreqY(k) = f_k(freqIdY+1);
    end
end

%% 绘制位移曲线对比图
figure('Position', [100, 100, 1400, 1000], 'Name', '位移分析结果（修正版）', 'NumberTitle', 'off');

% 全局运动曲线
subplot(4,1,1);
hold on; grid on;
plot(1:numFrames, globalMotion(:,1), 'k--', 'LineWidth', 1.2, 'DisplayName', '原始全局运动(X)');
plot(1:numFrames, globalMotionSmoothed(:,1), 'k-', 'LineWidth', 1.5, 'DisplayName', '平滑后全局运动(X)');
legend('show');
title(['X方向全局运动（无人机晃动）(', unit, ')']);
xlabel('图像序号'); ylabel(['位移(', unit, ')']);

subplot(4,1,2);
hold on; grid on;
plot(1:numFrames, globalMotion(:,2), 'k--', 'LineWidth', 1.2, 'DisplayName', '原始全局运动(Y)');
plot(1:numFrames, globalMotionSmoothed(:,2), 'k-', 'LineWidth', 1.5, 'DisplayName', '平滑后全局运动(Y)');
legend('show');
title(['Y方向全局运动（无人机晃动）(', unit, ')']);
xlabel('图像序号'); ylabel(['位移(', unit, ')']);

% 结构点消除晃动后的位移
subplot(4,1,3);
hold on; grid on;
plot(1:numFrames, zeros(1,numFrames), 'k-.', 'LineWidth', 1, 'DisplayName', '零线');
for k = 1:numStructurePoints
    plot(1:numFrames, absoluteDisplacementSmoothed(:,k,1), 'Color', colorNames{numFeaturePoints + k}, ...
        'LineWidth', 1.5, 'DisplayName', sprintf('结构点%d(X)', k));
    plot(maxIdxX(k), absoluteDisplacementSmoothed(maxIdxX(k),k,1), 'mo', 'MarkerSize', 8);
    text(maxIdxX(k), absoluteDisplacementSmoothed(maxIdxX(k),k,1), ...
        sprintf('  最大X: %.2f%s', maxDispX(k), unit), 'Color','m', 'FontName', 'SimHei');
end
legend('show');
title(['X方向桥梁位移（消除晃动后）(', unit, ')']);
xlabel('图像序号'); ylabel(['位移(', unit, ')']);

subplot(4,1,4);
hold on; grid on;
plot(1:numFrames, zeros(1,numFrames), 'k-.', 'LineWidth', 1, 'DisplayName', '零线');
for k = 1:numStructurePoints
    plot(1:numFrames, absoluteDisplacementSmoothed(:,k,2), 'Color', colorNames{numFeaturePoints + k}, ...
        'LineWidth', 1.5, 'DisplayName', sprintf('结构点%d(Y)', k));
    plot(maxIdxY(k), absoluteDisplacementSmoothed(maxIdxY(k),k,2), 'mo', 'MarkerSize', 8);
    text(maxIdxY(k), absoluteDisplacementSmoothed(maxIdxY(k),k,2), ...
        sprintf('  最大Y: %.2f%s', maxDispY(k), unit), 'Color','m', 'FontName', 'SimHei');
end
legend('show');
title(['Y方向桥梁位移（消除晃动后）(', unit, ')']);
xlabel('图像序号'); ylabel(['位移(', unit, ')']);

%% 保存结果
save(fullfile(savePath, 'displacement_results_corrected.mat'), ...
    'absoluteDisplacement', 'absoluteDisplacementSmoothed', 'globalMotion', 'globalMotionSmoothed', ...
    'correctedImages', 'markedImages', 'scaleFactor', 'frameRate', 'numFrames', ...
    'dominantFreqX', 'dominantFreqY', 'maxDispX', 'maxDispY');
fprintf('结果数据已保存至: %s\n', fullfile(savePath, 'displacement_results_corrected.mat'));

%% 显示分析结果
fprintf('\n=== 修正版分析结果 ===\n');
fprintf('全局运动统计:\n');
fprintf('  X方向最大晃动: %.2f%s\n', max(abs(globalMotionSmoothed(:,1))), unit);
fprintf('  Y方向最大晃动: %.2f%s\n', max(abs(globalMotionSmoothed(:,2))), unit);
fprintf('  晃动标准差: X=%.2f%s, Y=%.2f%s\n', std(globalMotionSmoothed(:,1)), unit, std(globalMotionSmoothed(:,2)), unit);

fprintf('\n桥梁位移测量结果:\n');
for k = 1:numStructurePoints
    fprintf('结构点%d:\n', k);
    fprintf('  X方向: 最大位移=%.4f%s, 主频率=%.4fHz\n', maxDispX(k), unit, dominantFreqX(k));
    fprintf('  Y方向: 最大位移=%.4f%s, 主频率=%.4fHz\n', maxDispY(k), unit, dominantFreqY(k));
    fprintf('  位移标准差: X=%.4f%s, Y=%.4f%s\n', ...
        std(absoluteDisplacementSmoothed(:,k,1)), unit, std(absoluteDisplacementSmoothed(:,k,2)), unit);
end

fprintf('\n修正效果评估:\n');
% 计算修正前后的位移稳定性
for k = 1:numStructurePoints
    originalStdX = std(strucFrameDisplacement(:,k,1));
    correctedStdX = std(absoluteDisplacementSmoothed(:,k,1));
    originalStdY = std(strucFrameDisplacement(:,k,2));
    correctedStdY = std(absoluteDisplacementSmoothed(:,k,2));

    improvementX = (originalStdX - correctedStdX) / originalStdX * 100;
    improvementY = (originalStdY - correctedStdY) / originalStdY * 100;

    fprintf('结构点%d稳定性改善: X方向%.1f%%, Y方向%.1f%%\n', k, improvementX, improvementY);
end

fprintf('\n程序执行完成！请查看修正后的图像和位移曲线。\n');
