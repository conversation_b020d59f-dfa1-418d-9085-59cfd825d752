# 无人机图像晃动消除程序使用说明

## 问题分析

您原始代码中的主要问题：

1. **累积位移计算错误**：将绝对位移当作增量位移来累积
2. **参考系不统一**：没有以第一帧为统一参考系进行图像修正
3. **单位转换混乱**：物理单位和像素单位之间的转换不正确

## 解决方案

### 核心修正逻辑

1. **正确的位移计算**：
   - `featFrameDisplacement(i,k,:)` 存储特征点相对于第一帧的绝对位移
   - `globalMotionSmoothed(i,:)` 直接表示第i帧相对于第一帧的绝对位移
   - 删除错误的累积计算：`cumulativeMotion = cumulativeMotion + (globalMotionSmoothed(i,:) - globalMotionSmoothed(i-1,:))`

2. **统一的图像修正**：
   - 所有图像都对齐到第一帧坐标系
   - 使用绝对位移进行图像变换：`correctRigidMotion(img, absoluteDx, absoluteDy)`

3. **正确的结构点位移计算**：
   - 桥梁真实位移 = 结构点绝对位移 - 全局运动
   - `realDispX = absDispX - globalMotionSmoothed(i,1)`

## 文件说明

### 1. `无人机桥梁位移测量_修正版.m`
完整的修正版程序，包含所有功能：
- 尺度标定
- 特征点和结构点选取
- 多尺度模板匹配
- RANSAC全局运动估计
- 图像刚体位移修正
- 结果分析和可视化

### 2. `图像晃动修正验证.m`
简化的验证程序，用于快速测试修正效果：
- 简化的交互界面
- 快速的模板匹配
- 直观的对比结果
- 适合初步验证

## 使用步骤

### 方法一：使用完整版程序

1. **运行程序**：
   ```matlab
   run('无人机桥梁位移测量_修正版.m')
   ```

2. **尺度标定**：
   - 在图像上点击两个已知距离的点
   - 输入实际距离（毫米）

3. **选择特征点**：
   - 选择3个固定不动的参考点（地面、山体等）
   - 确保纹理丰富，远离图像边界

4. **选择结构点**：
   - 选择2个桥梁上的测量点
   - 选择棱角或纹理清晰的区域

5. **查看结果**：
   - 程序会自动生成位移曲线对比图
   - 修正后的图像保存在 `results_corrected` 文件夹

### 方法二：使用验证程序（推荐先用这个）

1. **运行验证程序**：
   ```matlab
   run('图像晃动修正验证.m')
   ```

2. **选择参考点**：
   - 点击3个固定参考点
   - 点击1个桥梁测量点

3. **输入尺度因子**：
   - 如果不知道确切值，输入1进行相对测量

4. **查看结果**：
   - 程序会显示修正前后的对比图
   - 输出稳定性改善统计

## 关键改进点

### 1. 修正了累积位移计算
**原始错误代码**：
```matlab
cumulativeMotion = cumulativeMotion + (globalMotionSmoothed(i,:) - globalMotionSmoothed(i-1,:));
```

**修正后代码**：
```matlab
% 直接使用绝对位移
absoluteDx = globalMotionSmoothed(i,1) / scaleFactor;
absoluteDy = globalMotionSmoothed(i,2) / scaleFactor;
```

### 2. 统一了图像修正参考系
**修正后的图像变换函数**：
```matlab
function correctedImg = correctRigidMotion(img, absoluteDx, absoluteDy)
    [h, w, c] = size(img);
    tform = affine2d([1 0 0; 0 1 0; -absoluteDx -absoluteDy 1]);
    correctedImg = imwarp(img, tform, 'OutputView', imref2d([h w]), ...
        'Interp', 'bicubic', 'FillValues', [128 128 128]);
end
```

### 3. 正确的结构点位移计算
```matlab
% 计算消除无人机晃动后的真实桥梁位移
realDispX = absDispX - globalMotionSmoothed(i,1);
realDispY = absDispY - globalMotionSmoothed(i,2);
```

## 预期效果

使用修正版程序后，您应该看到：

1. **修正后的位移曲线更加平滑**：消除了无人机晃动的影响
2. **图像序列更加稳定**：连续查看修正后的图像时晃动明显减少
3. **位移测量更加准确**：结构点的位移反映真实的桥梁变形
4. **统计数据显示改善**：位移标准差显著降低

## 故障排除

1. **如果特征点跟踪失败**：
   - 选择纹理更丰富的区域
   - 避免光照变化剧烈的区域
   - 确保特征点不在图像边界附近

2. **如果修正效果不佳**：
   - 检查特征点是否真正固定不动
   - 增加特征点数量
   - 调整搜索范围和相关系数阈值

3. **如果程序运行缓慢**：
   - 减少图像数量进行测试
   - 降低模板尺寸
   - 使用验证程序进行快速测试

## 技术支持

如果遇到问题，请检查：
1. 图像文件格式是否正确（JPG）
2. 图像是否按时间顺序排列
3. MATLAB版本是否支持所需函数
4. 图像处理工具箱是否已安装
